# PowerShell script to test file upload
$uri = "http://localhost:3001/api/chat"
$filePath = "test_math_document.txt"
$query = "Please solve Problem 1 from this document step by step"

# Create multipart form data
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

# Read file content
$fileContent = Get-Content $filePath -Raw
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)

# Build multipart body
$bodyLines = @()
$bodyLines += "--$boundary"
$bodyLines += 'Content-Disposition: form-data; name="query"'
$bodyLines += ''
$bodyLines += $query
$bodyLines += "--$boundary"
$bodyLines += 'Content-Disposition: form-data; name="file"; filename="test_math_document.txt"'
$bodyLines += 'Content-Type: text/plain'
$bodyLines += ''

$body = ($bodyLines -join $LF) + $LF
$bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($body)
$bodyBytes += $fileBytes
$bodyBytes += [System.Text.Encoding]::UTF8.GetBytes("$LF--$boundary--$LF")

try {
    $response = Invoke-RestMethod -Uri $uri -Method POST -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyBytes
    Write-Host "SUCCESS! File upload and processing worked!"
    Write-Host "Query: $($response.query)"
    Write-Host "Extracted Text Length: $($response.extractedText.Length)"
    Write-Host "Response: $($response.response)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
