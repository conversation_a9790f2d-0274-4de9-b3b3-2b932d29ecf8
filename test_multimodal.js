const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Test configuration
const BASE_URL = 'http://localhost:3000';
const TEST_IMAGE_PATH = './test_image.png';
const TEST_DOC_PATH = './test_document.txt';

// Test 1: Text-only query
async function testTextOnly() {
  console.log('📝 Testing text-only query...');
  try {
    const response = await axios.post(`${BASE_URL}/api/chat`, {
      query: 'What is the capital of France?'
    });
    console.log('✅ Text-only test passed');
    console.log('Response:', response.data.response.substring(0, 100) + '...');
  } catch (error) {
    console.error('❌ Text-only test failed:', error.response?.data || error.message);
  }
}

// Test 2: Image + text query
async function testImageWithText() {
  console.log('\n🖼️ Testing image + text query...');
  try {
    if (!fs.existsSync(TEST_IMAGE_PATH)) {
      console.log('⚠️ Test image not found, skipping image test');
      return;
    }

    const form = new FormData();
    form.append('query', 'What do you see in this image?');
    form.append('file', fs.createReadStream(TEST_IMAGE_PATH));

    const response = await axios.post(`${BASE_URL}/api/chat`, form, {
      headers: form.getHeaders()
    });
    console.log('✅ Image + text test passed');
    console.log('Response:', response.data.response.substring(0, 100) + '...');
  } catch (error) {
    console.error('❌ Image + text test failed:', error.response?.data || error.message);
  }
}

// Test 3: Document + text query
async function testDocumentWithText() {
  console.log('\n📄 Testing document + text query...');
  try {
    if (!fs.existsSync(TEST_DOC_PATH)) {
      console.log('⚠️ Test document not found, creating sample...');
      fs.writeFileSync(TEST_DOC_PATH, 'This is a test document about machine learning and artificial intelligence.');
    }

    const form = new FormData();
    form.append('query', 'Summarize the content of this document');
    form.append('file', fs.createReadStream(TEST_DOC_PATH));

    const response = await axios.post(`${BASE_URL}/api/chat`, form, {
      headers: form.getHeaders()
    });
    console.log('✅ Document + text test passed');
    console.log('Response:', response.data.response.substring(0, 100) + '...');
  } catch (error) {
    console.error('❌ Document + text test failed:', error.response?.data || error.message);
  }
}

// Test 4: Health check
async function testHealthCheck() {
  console.log('\n🏥 Testing health check...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed');
    console.log('Status:', response.data.status);
  } catch (error) {
    console.error('❌ Health check failed:', error.response?.data || error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting multimodal functionality tests...\n');
  
  await testHealthCheck();
  await testTextOnly();
  await testImageWithText();
  await testDocumentWithText();
  
  console.log('\n🎯 All tests completed!');
}

// Check if server is running before starting tests
async function checkServer() {
  try {
    await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Server is running, starting tests...');
    runTests();
  } catch (error) {
    console.error('❌ Server is not running. Please start the server first:');
    console.error('   node index.js');
    process.exit(1);
  }
}

checkServer();