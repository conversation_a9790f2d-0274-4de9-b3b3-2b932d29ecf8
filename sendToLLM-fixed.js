// Clean version of sendTo<PERSON><PERSON> function with proper structure
async function sendToLLM(query, extractedText = '', fileInfo = null) {
  console.log('sendTo<PERSON><PERSON> called with:', {
    queryLength: query?.length || 0,
    hasExtractedText: !!extractedText,
    extractedTextLength: extractedText?.length || 0,
    fileInfo: fileInfo
  });

  const primaryModel = process.env.AI_MODEL_NAME;
  const fallbackModel = 'meta-llama/llama-3.2-3b-instruct:free';
  let contentType = { isScientific: false };
  let fullQuery = query;

  try {
    // Handle extracted text from files with better processing
    if (extractedText && extractedText.trim()) {
      const cleanExtractedText = extractedText.trim().replace(/\r\n/g, '\n').replace(/\r/g, '\n');

      if (cleanExtractedText.length === 0) {
        console.log('Warning: Extracted text is empty after cleaning');
        fullQuery = `${query}\n\n[Note: A file was uploaded but no readable text content was found.]`;
      } else {
        fullQuery = `User Query: ${query}\n\nExtracted Content:\n${cleanExtractedText}`;
        console.log(`Combined query length: ${fullQuery.length} characters`);

        if (fullQuery.length > 12000) {
          const maxExtractedLength = 12000 - query.length - 300;
          const truncatedText = cleanExtractedText.substring(0, maxExtractedLength) + '\n\n[Content truncated due to length...]';
          fullQuery = `User Query: ${query}\n\nExtracted Content (truncated):\n${truncatedText}`;
          console.log(`Query truncated to ${fullQuery.length} characters`);
        }
      }
    }

    // Detect content type for specialized handling
    contentType = detectContentType(fullQuery);

    // Special preprocessing for high-complexity problems
    if (contentType.isHighComplexity) {
      console.log('🧠 Detected high-complexity problem, applying specialized preprocessing...');
      
      if (contentType.isOptics && contentType.isComplexProblem) {
        fullQuery = `${fullQuery}\n\n[SYSTEM NOTE: This appears to be a complex optics problem with multiple parts. Please solve systematically, showing all steps clearly, and break down the solution into manageable sections.]`;
      } else if (contentType.isComplexProblem) {
        fullQuery = `${fullQuery}\n\n[SYSTEM NOTE: This appears to be a multi-step problem. Please solve each part systematically and show all intermediate steps clearly.]`;
      }
    }

    // Generate specialized system prompt
    const systemPrompt = generateSystemPrompt(contentType);

    // Configure request parameters based on content type
    let maxTokens = 4000;
    let temperature = 0.7;
    let timeout = 90000;

    if (contentType.isScientific) {
      maxTokens = 8000;
      temperature = 0.2;
      timeout = 120000;

      if (contentType.isAdvanced || contentType.isHighComplexity) {
        maxTokens = 10000;
        timeout = 180000;
      }

      if (contentType.isMath && contentType.isAdvanced) {
        temperature = 0.1;
      }
      
      if (contentType.isOptics && contentType.isComplexProblem) {
        maxTokens = 12000;
        timeout = 240000;
        temperature = 0.15;
      }
    }

    const requestConfig = {
      model: primaryModel,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: fullQuery
        }
      ],
      max_tokens: maxTokens,
      temperature: temperature
    };

    const axiosConfig = {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://jubuddy-ai-backend.onrender.com',
        'X-Title': 'JUBuddyAI Backend - Scientific AI Assistant'
      },
      timeout: timeout
    };

    let response;
    let modelUsed = primaryModel;

    // Retry mechanism for complex problems
    const maxRetries = contentType.isHighComplexity ? 2 : 1;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        console.log(`Processing ${contentType.isScientific ? 'scientific' : 'general'} content with ${primaryModel} (attempt ${retryCount + 1}/${maxRetries + 1})`);
        console.log(`Query preview: ${fullQuery.substring(0, 200)}...`);
        
        if (retryCount > 0) {
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
        }
        
        response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, requestConfig, axiosConfig);
        break;
      } catch (retryError) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw retryError;
        }
        console.log(`Attempt ${retryCount} failed, retrying... Error: ${retryError.message}`);
      }
    }

    // If primary model failed completely, try fallback approaches
    if (!response) {
      console.log('Primary model failed, trying fallback approaches...');
      
      // For complex problems, try chunking approach first
      if (contentType.isHighComplexity) {
        try {
          console.log('🔄 Attempting chunked processing for complex problem...');
          const chunks = chunkComplexProblem(fullQuery, contentType);
          
          if (chunks.length > 1) {
            console.log(`Breaking problem into ${chunks.length} chunks`);
            let combinedResponse = '';
            
            for (let i = 0; i < chunks.length && i < 3; i++) {
              const chunkConfig = {
                model: primaryModel,
                messages: [
                  {
                    role: 'system',
                    content: `${systemPrompt} Focus on this specific part of a larger problem. Provide a complete solution for this section.`
                  },
                  {
                    role: 'user',
                    content: `Part ${i + 1} of ${chunks.length}:\n\n${chunks[i]}`
                  }
                ],
                max_tokens: 4000,
                temperature: temperature
              };
              
              const chunkResponse = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, chunkConfig, axiosConfig);
              if (chunkResponse.data?.choices?.[0]?.message?.content) {
                combinedResponse += `\n\n**Part ${i + 1} Solution:**\n${chunkResponse.data.choices[0].message.content}`;
              }
            }
            
            if (combinedResponse.trim()) {
              console.log('✅ Successfully processed complex problem using chunking approach');
              return `**Complex Problem Solution (Processed in Parts):**${combinedResponse}`;
            }
          }
        } catch (chunkError) {
          console.log('Chunking approach failed, trying fallback model...');
        }
      }

      // Try fallback model with simplified request
      try {
        const fallbackConfig = {
          model: fallbackModel,
          messages: [
            {
              role: 'system',
              content: contentType.isScientific 
                ? 'You are a helpful AI assistant specializing in scientific content. Provide clear, step-by-step solutions to scientific problems.'
                : 'You are a helpful AI assistant. Provide clear, accurate responses to user queries.'
            },
            {
              role: 'user',
              content: fullQuery.length > 8000 ? fullQuery.substring(0, 8000) + '\n\n[Content truncated for fallback processing]' : fullQuery
            }
          ],
          max_tokens: contentType.isScientific ? 6000 : 3000,
          temperature: contentType.isScientific ? 0.3 : 0.7
        };

        modelUsed = fallbackModel;
        console.log(`Retrying with fallback model: ${fallbackModel}`);
        response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, fallbackConfig, axiosConfig);
      } catch (fallbackError) {
        console.error('Fallback model also failed:', fallbackError.message);
        throw new Error('All models failed to process the request');
      }
    }

    // Validate response
    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('Invalid response structure from LLM API');
    }

    const content = response.data.choices[0].message.content;
    if (!content || content.trim() === '') {
      console.error('Empty response received from AI model:', {
        model: modelUsed,
        hasExtractedText: !!extractedText,
        queryLength: fullQuery?.length || 0,
        contentType: contentType,
        responseData: response.data
      });
      
      let errorMessage = 'Empty response from AI model';
      if (contentType.isHighComplexity) {
        errorMessage += ' - Complex problems may require breaking down into smaller parts. Try asking about one specific aspect of the problem at a time.';
      } else if (contentType.isScientific) {
        errorMessage += ' - This may be due to content filtering for scientific content. Try rephrasing your question or providing more context.';
      } else {
        errorMessage += ' - This may be due to content filtering, rate limits, or model issues. Please try rephrasing your question.';
      }
      
      throw new Error(errorMessage);
    }

    // Log successful processing
    console.log(`Successfully processed ${contentType.isScientific ? 'scientific' : 'general'} content using ${modelUsed}`);

    const responseMetadata = {
      contentType: contentType,
      modelUsed: modelUsed,
      tokensUsed: response.data.usage?.total_tokens || 'unknown',
      processingTime: Date.now()
    };

    console.log('Response metadata:', responseMetadata);
    return content;

  } catch (error) {
    console.error('LLM API Error Details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      timeout: error.code === 'ECONNABORTED',
      contentType: contentType,
      isScientific: contentType?.isScientific || false
    });

    if (error.code === 'ECONNABORTED') {
      const timeoutMsg = contentType?.isScientific
        ? 'Request timeout: Complex scientific problems can take time to process. Please try breaking your question into smaller parts or simplifying the problem.'
        : 'Request timeout: The AI is taking too long to process your content. Please try with a simpler question.';
      throw new Error(timeoutMsg);
    } else if (error.response) {
      const errorMsg = error.response.data?.error?.message || error.response.statusText || 'Unknown API error';
      const scientificNote = contentType?.isScientific
        ? ' Note: This appears to be scientific content. If the error persists, try rephrasing your question or breaking it into smaller parts.'
        : '';
      throw new Error(`LLM API error: ${errorMsg}${scientificNote}`);
    } else if (error.request) {
      throw new Error('LLM API request failed: No response received from the server. Please check your internet connection and try again.');
    } else {
      throw new Error(`Unexpected error: ${error.message}`);
    }
  }
}

module.exports = { sendToLLM };
