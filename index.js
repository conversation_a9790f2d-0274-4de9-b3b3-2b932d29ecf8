const express = require('express');
const multer = require('multer');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const { createWorker } = require('tesseract.js');
const pdf = require('pdf-parse');
const mammoth = require('mammoth');
const cors = require('cors');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype) || 
                     file.mimetype === 'application/pdf' ||
                     file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only .jpg, .png, .pdf, and .docx files are allowed'));
    }
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'JUBuddyAI Backend is running' });
});

// Extract text from image using Tesseract.js
async function extractTextFromImage(imagePath) {
  try {
    const worker = await createWorker();
    await worker.loadLanguage('eng');
    await worker.initialize('eng');
    
    const { data: { text } } = await worker.recognize(imagePath);
    await worker.terminate();
    
    return text.trim();
  } catch (error) {
    throw new Error(`OCR failed: ${error.message}`);
  }
}

// Extract text from PDF
async function extractTextFromPDF(pdfPath) {
  try {
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdf(dataBuffer);
    return data.text.trim();
  } catch (error) {
    throw new Error(`PDF parsing failed: ${error.message}`);
  }
}

// Extract text from DOCX
async function extractTextFromDOCX(docxPath) {
  try {
    const result = await mammoth.extractRawText({ path: docxPath });
    return result.value.trim();
  } catch (error) {
    throw new Error(`DOCX parsing failed: ${error.message}`);
  }
}

// Send query to OpenRouter API with fallback for mathematical content
async function sendToLLM(query, extractedText = '') {
  const primaryModel = process.env.AI_MODEL_NAME;
  const fallbackModel = 'meta-llama/llama-3.2-3b-instruct:free'; // Good fallback for math

  try {
    let fullQuery = query;
    if (extractedText) {
      fullQuery = `User Query: ${query}\n\nExtracted Content: ${extractedText}`;
    }

    // Detect if content might be mathematical
    const isMathContent = /[\d\+\-\*\/\=\(\)\^\√∫∑∏π∞≤≥≠±∆∇∂∈∉∪∩⊂⊃∀∃∴∵∠°]/.test(fullQuery) ||
                         /\b(solve|calculate|equation|formula|derivative|integral|limit|matrix|algebra|geometry|trigonometry|calculus|statistics|probability)\b/i.test(fullQuery);

    // Add system message for better mathematical handling
    const messages = [
      {
        role: 'system',
        content: isMathContent
          ? 'You are a helpful AI assistant specialized in mathematics. When solving mathematical problems, always provide step-by-step solutions. Break down complex expressions clearly, show your work, and explain each step. If you encounter mathematical symbols or equations, interpret them carefully and solve them systematically.'
          : 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries.'
      },
      {
        role: 'user',
        content: fullQuery
      }
    ];

    const requestConfig = {
      model: primaryModel,
      messages: messages,
      max_tokens: isMathContent ? 6000 : 4000, // More tokens for math explanations
      temperature: isMathContent ? 0.3 : 0.7, // Lower temperature for math precision
      timeout: 90000 // 90 second timeout for complex math
    };

    const axiosConfig = {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://jubuddy-ai-backend.onrender.com',
        'X-Title': 'JUBuddyAI Backend'
      },
      timeout: 90000 // 90 second timeout for axios
    };

    let response;
    try {
      // Try primary model first
      response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, requestConfig, axiosConfig);
    } catch (primaryError) {
      console.log('Primary model failed, trying fallback model for mathematical content...');

      // If primary model fails and it's math content, try fallback
      if (isMathContent) {
        requestConfig.model = fallbackModel;
        response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, requestConfig, axiosConfig);
      } else {
        throw primaryError; // Re-throw if not math content
      }
    }

    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('Invalid response structure from LLM API');
    }

    const content = response.data.choices[0].message.content;
    if (!content || content.trim() === '') {
      throw new Error('Empty response from AI model');
    }

    return content;
  } catch (error) {
    console.error('LLM API Error Details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      timeout: error.code === 'ECONNABORTED',
      model: error.config?.data ? JSON.parse(error.config.data).model : 'unknown'
    });

    if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout: The AI is taking too long to process your content. Please try with a simpler question or break it into smaller parts.');
    } else if (error.response) {
      const errorMsg = error.response.data?.error?.message || error.response.statusText || 'Unknown API error';
      throw new Error(`LLM API error: ${errorMsg}`);
    } else if (error.request) {
      throw new Error('LLM API request failed: No response received from the server. Please check your internet connection and try again.');
    } else {
      throw new Error(`LLM API setup error: ${error.message}`);
    }
  }
}

// Main chat endpoint (legacy)
app.post('/chat', upload.single('file'), async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string' || query.trim() === '') {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    let extractedText = '';

    if (req.file) {
      const filePath = req.file.path;
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      try {
        if (fileExtension === '.jpg' || fileExtension === '.jpeg' || fileExtension === '.png') {
          extractedText = await extractTextFromImage(filePath);
        } else if (fileExtension === '.pdf') {
          extractedText = await extractTextFromPDF(filePath);
        } else if (fileExtension === '.docx') {
          extractedText = await extractTextFromDOCX(filePath);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);
      } catch (error) {
        // Clean up file on error
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        throw error;
      }
    }

    if (req.file && !extractedText) {
      return res.status(400).json({
        error: 'No text could be extracted from the provided file'
      });
    }

    const llmResponse = await sendToLLM(query.trim(), extractedText);

    // Validate response
    if (!llmResponse || typeof llmResponse !== 'string' || llmResponse.trim() === '') {
      throw new Error('Empty or invalid response from AI model');
    }

    res.json({
      success: true,
      query: query.trim(),
      extractedText: extractedText || null,
      response: llmResponse.trim()
    });

  } catch (error) {
    console.error('Error in /chat endpoint:', error);

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // Provide more specific error messages
    let errorMessage = 'An internal server error occurred';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout: Please try again with a simpler question';
      statusCode = 408;
    } else if (error.message.includes('LLM API error')) {
      errorMessage = error.message;
      statusCode = 502;
    } else if (error.message.includes('Empty or invalid response')) {
      errorMessage = 'The AI model returned an empty response. Please try rephrasing your question.';
      statusCode = 502;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
});

// API versioned chat endpoint
app.post('/api/chat', upload.single('file'), async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string' || query.trim() === '') {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    let extractedText = '';

    if (req.file) {
      const filePath = req.file.path;
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      try {
        if (fileExtension === '.jpg' || fileExtension === '.jpeg' || fileExtension === '.png') {
          extractedText = await extractTextFromImage(filePath);
        } else if (fileExtension === '.pdf') {
          extractedText = await extractTextFromPDF(filePath);
        } else if (fileExtension === '.docx') {
          extractedText = await extractTextFromDOCX(filePath);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);
      } catch (error) {
        // Clean up file on error
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        throw error;
      }
    }

    if (req.file && !extractedText) {
      return res.status(400).json({
        error: 'No text could be extracted from the provided file'
      });
    }

    const llmResponse = await sendToLLM(query.trim(), extractedText);

    // Validate response
    if (!llmResponse || typeof llmResponse !== 'string' || llmResponse.trim() === '') {
      throw new Error('Empty or invalid response from AI model');
    }

    res.json({
      success: true,
      query: query.trim(),
      extractedText: extractedText || null,
      response: llmResponse.trim()
    });

  } catch (error) {
    console.error('Error in /api/chat endpoint:', error);

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // Provide more specific error messages
    let errorMessage = 'An internal server error occurred';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout: Please try again with a simpler question';
      statusCode = 408;
    } else if (error.message.includes('LLM API error')) {
      errorMessage = error.message;
      statusCode = 502;
    } else if (error.message.includes('Empty or invalid response')) {
      errorMessage = 'The AI model returned an empty response. Please try rephrasing your question.';
      statusCode = 502;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB' });
    }
    return res.status(400).json({ error: error.message });
  }
  
  if (error.message.includes('Only .jpg, .png, .pdf, and .docx files are allowed')) {
    return res.status(400).json({ error: error.message });
  }

  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(port, () => {
  console.log(`JUBuddyAI Backend running on port ${port}`);
  console.log(`Health check: http://localhost:${port}/health`);
});