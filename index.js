const express = require('express');
const multer = require('multer');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const { createWorker } = require('tesseract.js');
const pdf = require('pdf-parse');
const mammoth = require('mammoth');
const cors = require('cors');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf|docx/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype) || 
                     file.mimetype === 'application/pdf' ||
                     file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only .jpg, .png, .pdf, and .docx files are allowed'));
    }
  }
});

// Health check endpoint with scientific capabilities info
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'JUBuddyAI Backend is running',
    capabilities: {
      scientific_fields: [
        'Mathematics (Basic to PhD level)',
        'Physics (Classical to Quantum)',
        'Chemistry (General to Advanced Organic/Inorganic)',
        'Statistics (Descriptive to Advanced Inference)',
        'Biology (Cell Biology to Systems Biology)',
        'Advanced Sciences (Computational, Theoretical, Applied)'
      ],
      features: [
        'Step-by-step problem solving',
        'Multi-level content detection',
        'Specialized scientific prompts',
        'Enhanced error handling',
        'Fallback model support',
        'OCR for scientific documents',
        'PDF/DOCX processing'
      ],
      models: {
        primary: process.env.AI_MODEL_NAME,
        fallback: 'meta-llama/llama-3.2-3b-instruct:free'
      }
    }
  });
});

// Scientific capabilities endpoint
app.get('/capabilities', (req, res) => {
  res.json({
    scientific_fields: {
      mathematics: {
        levels: ['Elementary', 'High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['Arithmetic', 'Algebra', 'Geometry', 'Trigonometry', 'Calculus', 'Linear Algebra', 'Differential Equations', 'Complex Analysis', 'Real Analysis', 'Abstract Algebra', 'Topology', 'Number Theory', 'Mathematical Logic', 'Set Theory']
      },
      physics: {
        levels: ['High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Optics', 'Modern Physics', 'Quantum Mechanics', 'Relativity', 'Statistical Mechanics', 'Condensed Matter', 'Particle Physics', 'Astrophysics', 'Nuclear Physics']
      },
      chemistry: {
        levels: ['High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['General Chemistry', 'Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Analytical Chemistry', 'Biochemistry', 'Materials Chemistry', 'Computational Chemistry', 'Medicinal Chemistry']
      },
      statistics: {
        levels: ['Basic', 'Intermediate', 'Advanced', 'PhD'],
        topics: ['Descriptive Statistics', 'Probability', 'Hypothesis Testing', 'Regression Analysis', 'ANOVA', 'Bayesian Statistics', 'Time Series', 'Machine Learning', 'Experimental Design', 'Biostatistics']
      },
      biology: {
        levels: ['High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['Cell Biology', 'Genetics', 'Evolution', 'Ecology', 'Molecular Biology', 'Biochemistry', 'Neuroscience', 'Developmental Biology', 'Systems Biology', 'Bioinformatics']
      }
    },
    processing_features: {
      content_detection: 'Automatic detection of scientific content type',
      specialized_prompts: 'Field-specific system prompts for optimal responses',
      step_by_step: 'Detailed step-by-step solutions for problems',
      error_handling: 'Enhanced error handling for complex scientific queries',
      fallback_models: 'Multiple AI models for reliability',
      file_processing: 'OCR and document processing for scientific papers'
    }
  });
});

// Extract text from image using Tesseract.js
async function extractTextFromImage(imagePath) {
  try {
    const worker = await createWorker();
    await worker.loadLanguage('eng');
    await worker.initialize('eng');
    
    const { data: { text } } = await worker.recognize(imagePath);
    await worker.terminate();
    
    return text.trim();
  } catch (error) {
    throw new Error(`OCR failed: ${error.message}`);
  }
}

// Extract text from PDF
async function extractTextFromPDF(pdfPath) {
  try {
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdf(dataBuffer);
    return data.text.trim();
  } catch (error) {
    throw new Error(`PDF parsing failed: ${error.message}`);
  }
}

// Extract text from DOCX
async function extractTextFromDOCX(docxPath) {
  try {
    const result = await mammoth.extractRawText({ path: docxPath });
    return result.value.trim();
  } catch (error) {
    throw new Error(`DOCX parsing failed: ${error.message}`);
  }
}

// Convert image file to base64
function imageToBase64(imagePath) {
  try {
    const imageBuffer = fs.readFileSync(imagePath);
    return imageBuffer.toString('base64');
  } catch (error) {
    throw new Error(`Failed to convert image to base64: ${error.message}`);
  }
}

// Get image mimetype from file extension
function getImageMimeType(fileExtension) {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
  };
  return mimeTypes[fileExtension.toLowerCase()] || 'image/jpeg';
}

// Enhanced scientific content detection and processing
function detectContentType(text) {
  const content = text.toLowerCase();

  // Mathematical content detection
  const mathPatterns = [
    /[\d\+\-\*\/\=\(\)\^\√∫∑∏π∞≤≥≠±∆∇∂∈∉∪∩⊂⊃∀∃∴∵∠°]/,
    /\b(solve|calculate|equation|formula|derivative|integral|limit|matrix|algebra|geometry|trigonometry|calculus|statistics|probability|theorem|proof|logarithm|exponential|polynomial|quadratic|linear|differential|partial|vector|scalar|eigenvalue|eigenvector)\b/i
  ];

  // Physics content detection
  const physicsPatterns = [
    /\b(force|energy|momentum|velocity|acceleration|mass|gravity|friction|pressure|temperature|heat|thermodynamics|quantum|relativity|electromagnetic|wave|frequency|amplitude|photon|electron|proton|neutron|atom|nuclear|radioactive|mechanics|kinematics|dynamics|optics|electricity|magnetism|circuit|voltage|current|resistance|capacitance|inductance|field|potential|work|power|entropy|enthalpy|oscillation|pendulum|spring|collision|conservation|newton|einstein|planck|bohr|schrodinger|maxwell|faraday|coulomb|ohm|joule|watt|pascal|kelvin|celsius|fahrenheit)\b/i
  ];

  // Chemistry content detection
  const chemistryPatterns = [
    /\b(molecule|atom|element|compound|reaction|bond|ionic|covalent|metallic|oxidation|reduction|acid|base|ph|buffer|catalyst|enzyme|equilibrium|kinetics|thermochemistry|electrochemistry|organic|inorganic|polymer|isomer|stereochemistry|spectroscopy|chromatography|titration|molarity|molality|stoichiometry|periodic|table|electron|configuration|orbital|hybridization|resonance|lewis|structure|vsepr|intermolecular|van der waals|hydrogen bonding|solubility|precipitation|crystallization|distillation|extraction|synthesis|mechanism|nucleophile|electrophile|substitution|elimination|addition|aromatic|aliphatic|functional group|carbohydrate|protein|lipid|nucleic acid|amino acid|peptide|enzyme|metabolism|photosynthesis|respiration)\b/i
  ];

  // Statistics content detection
  const statisticsPatterns = [
    /\b(mean|median|mode|variance|deviation|correlation|regression|hypothesis|test|significance|confidence|interval|distribution|normal|binomial|poisson|chi.?square|t.?test|anova|p.?value|null|alternative|sample|population|random|probability|bayes|likelihood|estimation|inference|descriptive|inferential|parametric|nonparametric|outlier|quartile|percentile|histogram|boxplot|scatterplot|contingency|table|survey|experiment|observational|study|bias|confounding|randomization|stratification|cluster|sampling|bootstrap|monte carlo|markov|chain|time series|forecasting|machine learning|data mining|big data|analytics)\b/i
  ];

  // Biology content detection
  const biologyPatterns = [
    /\b(cell|dna|rna|protein|gene|chromosome|mitosis|meiosis|evolution|natural selection|mutation|adaptation|species|ecosystem|biodiversity|photosynthesis|respiration|metabolism|enzyme|hormone|neuron|brain|nervous system|immune system|circulatory|respiratory|digestive|excretory|reproductive|endocrine|muscular|skeletal|integumentary|homeostasis|genetics|heredity|allele|genotype|phenotype|dominant|recessive|mendel|darwin|taxonomy|classification|kingdom|phylum|class|order|family|genus|bacteria|virus|fungi|plant|animal|vertebrate|invertebrate|mammal|bird|reptile|amphibian|fish|arthropod|mollusk|cnidarian|ecology|biome|habitat|niche|food chain|food web|producer|consumer|decomposer|symbiosis|parasitism|mutualism|commensalism|population|community|succession|conservation|endangered|extinction|biodiversity|biotechnology|genetic engineering|cloning|stem cell|cancer|disease|pathogen|antibiotic|vaccine|immunity)\b/i
  ];

  // Advanced scientific fields detection
  const advancedPatterns = [
    /\b(quantum mechanics|relativity|thermodynamics|statistical mechanics|solid state|condensed matter|particle physics|nuclear physics|astrophysics|cosmology|string theory|field theory|group theory|topology|differential geometry|complex analysis|functional analysis|measure theory|stochastic|processes|partial differential|equations|numerical analysis|computational|fluid dynamics|materials science|nanotechnology|biochemistry|molecular biology|biophysics|neuroscience|cognitive science|artificial intelligence|machine learning|deep learning|neural networks|algorithms|data structures|complexity theory|cryptography|information theory|signal processing|control theory|optimization|operations research|game theory|decision theory|econometrics|mathematical finance|actuarial science|epidemiology|biostatistics|clinical trials|meta analysis|systematic review)\b/i
  ];

  const isMath = mathPatterns.some(pattern => pattern.test(content));
  const isPhysics = physicsPatterns.some(pattern => pattern.test(content));
  const isChemistry = chemistryPatterns.some(pattern => pattern.test(content));
  const isStatistics = statisticsPatterns.some(pattern => pattern.test(content));
  const isBiology = biologyPatterns.some(pattern => pattern.test(content));
  const isAdvanced = advancedPatterns.some(pattern => pattern.test(content));

  return {
    isMath,
    isPhysics,
    isChemistry,
    isStatistics,
    isBiology,
    isAdvanced,
    isScientific: isMath || isPhysics || isChemistry || isStatistics || isBiology || isAdvanced
  };
}

// Generate specialized system prompts based on content type
function generateSystemPrompt(contentType) {
  if (!contentType.isScientific) {
    return 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries.';
  }

  let prompt = 'You are an expert AI assistant specializing in scientific and academic content. ';

  if (contentType.isMath) {
    prompt += 'For mathematical problems: (1) Provide step-by-step solutions, (2) Show all work clearly, (3) Explain each step, (4) Use proper mathematical notation, (5) Verify your answers when possible. ';
  }

  if (contentType.isPhysics) {
    prompt += 'For physics problems: (1) Identify given information and what to find, (2) State relevant principles/laws, (3) Set up equations systematically, (4) Solve step-by-step with units, (5) Check if the answer makes physical sense, (6) Explain the underlying physics concepts. ';
  }

  if (contentType.isChemistry) {
    prompt += 'For chemistry problems: (1) Identify the type of chemical process, (2) Write balanced chemical equations, (3) Show stoichiometric calculations, (4) Include proper chemical nomenclature, (5) Explain reaction mechanisms when relevant, (6) Consider thermodynamics and kinetics. ';
  }

  if (contentType.isStatistics) {
    prompt += 'For statistics problems: (1) Identify the type of statistical test/analysis needed, (2) State assumptions clearly, (3) Show calculations step-by-step, (4) Interpret results in context, (5) Discuss limitations and potential sources of error, (6) Use proper statistical terminology. ';
  }

  if (contentType.isBiology) {
    prompt += 'For biology questions: (1) Use accurate scientific terminology, (2) Explain biological processes clearly, (3) Connect structure to function, (4) Discuss evolutionary context when relevant, (5) Include molecular/cellular details as appropriate, (6) Reference current scientific understanding. ';
  }

  if (contentType.isAdvanced) {
    prompt += 'For advanced topics: (1) Provide rigorous explanations appropriate for graduate/PhD level, (2) Include relevant mathematical formulations, (3) Discuss current research and open questions, (4) Reference key papers/researchers when appropriate, (5) Explain both theoretical foundations and practical applications. ';
  }

  prompt += 'Always maintain scientific accuracy, cite established principles, and indicate when something is beyond current scientific knowledge.';

  return prompt;
}

// Send query to OpenRouter API with enhanced scientific content handling
async function sendToLLM(query, extractedText = '', fileInfo = null) {
  const primaryModel = process.env.AI_MODEL_NAME;
  const fallbackModel = 'meta-llama/llama-3.2-3b-instruct:free';
  let contentType = { isScientific: false }; // Initialize with default

  try {
    let fullQuery = query;
    let messages = [];
    
    // Handle multimodal content properly
    if (fileInfo && fileInfo.type === 'image' && fileInfo.data) {
      // For images, use multimodal format
      messages = [
        {
          role: 'system',
          content: 'You are a helpful AI assistant that can analyze images and text. Provide detailed, accurate responses based on the provided image and text.'
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: query
            },
            {
              type: 'image_url',
              image_url: {
                url: `data:${fileInfo.mimetype};base64,${fileInfo.data}`
              }
            }
          ]
        }
      ];
    } else {
      // For text-based content (including extracted text from documents)
      if (extractedText) {
        fullQuery = `User Query: ${query}\n\nExtracted Content: ${extractedText}`;
      }

      // Detect content type for specialized handling
      contentType = detectContentType(fullQuery);

      // Generate specialized system prompt
      const systemPrompt = generateSystemPrompt(contentType);

      messages = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: fullQuery
        }
      ];
    }

    // Configure request parameters based on content type
    let maxTokens = 4000; // Default
    let temperature = 0.7; // Default
    let timeout = 90000; // Default 90 seconds

    // Check if model supports multimodal (vision) capabilities
    const supportsMultimodal = !primaryModel.includes('llama') &&
                              !primaryModel.includes('mistral') &&
                              !primaryModel.includes('claude-instant');
    
    // If image processing is requested but model doesn't support it, use fallback
    let effectiveModel = primaryModel;
    if (fileInfo?.type === 'image' && !supportsMultimodal) {
      console.log(`Primary model ${primaryModel} doesn't support images, using fallback`);
      effectiveModel = 'openai/gpt-4o-mini'; // Use a model that supports vision
    }

    if (contentType.isScientific && fileInfo?.type !== 'image') {
      maxTokens = 8000; // More tokens for detailed scientific explanations
      temperature = 0.2; // Lower temperature for scientific accuracy
      timeout = 120000; // 2 minutes for complex scientific problems

      // Further adjustments for specific fields
      if (contentType.isAdvanced) {
        maxTokens = 10000; // Even more tokens for PhD-level content
        timeout = 180000; // 3 minutes for very complex problems
      }

      if (contentType.isMath && contentType.isAdvanced) {
        temperature = 0.1; // Highest precision for advanced mathematics
      }
    }

    const requestConfig = {
      model: effectiveModel,
      messages: messages,
      max_tokens: maxTokens,
      temperature: temperature,
      timeout: timeout,
      // Add additional parameters for better responses
      top_p: contentType.isScientific && fileInfo?.type !== 'image' ? 0.9 : 1.0,
      frequency_penalty: 0.1,
      presence_penalty: 0.1
    };

    const axiosConfig = {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://jubuddy-ai-backend.onrender.com',
        'X-Title': 'JUBuddyAI Backend - Scientific AI Assistant'
      },
      timeout: timeout
    };

    let response;
    let modelUsed = primaryModel;

    try {
      // Try primary model first
      console.log(`Processing ${fileInfo?.type || 'text'} content with ${primaryModel}`);
      response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, requestConfig, axiosConfig);
    } catch (primaryError) {
      console.log('Primary model failed, trying fallback model...');
      console.error('Primary model error:', primaryError.message);

      // Try fallback model
      try {
        requestConfig.model = fallbackModel;
        modelUsed = fallbackModel;
        // Adjust parameters for fallback model
        requestConfig.max_tokens = Math.min(requestConfig.max_tokens, 4000);
        axiosConfig.timeout = Math.min(timeout, 90000);

        console.log(`Retrying with fallback model: ${fallbackModel}`);
        response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, requestConfig, axiosConfig);
      } catch (fallbackError) {
        console.error('Fallback model also failed:', fallbackError.message);
        throw primaryError; // Throw original error if fallback also fails
      }
    }

    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('Invalid response structure from LLM API');
    }

    const content = response.data.choices[0].message.content;
    if (!content || content.trim() === '') {
      throw new Error('Empty response from AI model');
    }

    // Log successful processing for monitoring
    console.log(`Successfully processed ${fileInfo?.type || 'text'} content using ${modelUsed}`);

    // Add metadata about the processing for debugging
    const responseMetadata = {
      contentType: contentType,
      modelUsed: modelUsed,
      fileType: fileInfo?.type || 'text',
      tokensUsed: response.data.usage?.total_tokens || 'unknown',
      processingTime: Date.now()
    };

    console.log('Response metadata:', responseMetadata);

    return content;
  } catch (error) {
    console.error('LLM API Error Details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      timeout: error.code === 'ECONNABORTED',
      model: error.config?.data ? JSON.parse(error.config.data).model : 'unknown',
      contentType: contentType,
      fileType: fileInfo?.type || 'text',
      isScientific: contentType?.isScientific || false
    });

    if (error.code === 'ECONNABORTED') {
      const timeoutMsg = contentType?.isScientific
        ? 'Request timeout: Complex scientific problems can take time to process. Please try breaking your question into smaller parts or simplifying the problem.'
        : 'Request timeout: The AI is taking too long to process your content. Please try with a simpler question.';
      throw new Error(timeoutMsg);
    } else if (error.response) {
      const errorMsg = error.response.data?.error?.message || error.response.statusText || 'Unknown API error';
      const scientificNote = contentType?.isScientific
        ? ' Note: This appears to be scientific content. If the error persists, try rephrasing your question or breaking it into smaller parts.'
        : '';
      throw new Error(`LLM API error: ${errorMsg}${scientificNote}`);
    } else if (error.request) {
      throw new Error('LLM API request failed: No response received from the server. Please check your internet connection and try again.');
    } else {
      throw new Error(`LLM API setup error: ${error.message}`);
    }
  }
}

// Main chat endpoint (legacy)
app.post('/chat', upload.single('file'), async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string' || query.trim() === '') {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    let extractedText = '';
    let fileInfo = null;

    if (req.file) {
      const filePath = req.file.path;
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      try {
        if (fileExtension === '.jpg' || fileExtension === '.jpeg' || fileExtension === '.png') {
          // For images, use multimodal processing
          fileInfo = {
            type: 'image',
            data: imageToBase64(filePath),
            mimetype: getImageMimeType(fileExtension)
          };
        } else if (fileExtension === '.pdf') {
          extractedText = await extractTextFromPDF(filePath);
        } else if (fileExtension === '.docx') {
          extractedText = await extractTextFromDOCX(filePath);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);
      } catch (error) {
        // Clean up file on error
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        throw error;
      }
    }

    // Handle cases where no text was extracted but we have an image
    if (req.file && !fileInfo && !extractedText) {
      return res.status(400).json({
        error: 'No text could be extracted from the provided file'
      });
    }

    // If we have an image but OCR failed, still process the image directly
    if (fileInfo?.type === 'image' && !extractedText && query.trim()) {
      console.log('Processing image directly without OCR text');
    }

    // If we have extracted text but it's very short, enhance it
    if (extractedText && extractedText.trim().length < 10) {
      console.log('Extracted text is very short, may need additional context');
    }

    const llmResponse = await sendToLLM(query.trim(), extractedText, fileInfo);

    // Validate response
    if (!llmResponse || typeof llmResponse !== 'string' || llmResponse.trim() === '') {
      throw new Error('Empty or invalid response from AI model');
    }

    res.json({
      success: true,
      query: query.trim(),
      extractedText: extractedText || null,
      response: llmResponse.trim()
    });

  } catch (error) {
    console.error('Error in /chat endpoint:', error);

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // Provide more specific error messages
    let errorMessage = 'An internal server error occurred';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout: Please try again with a simpler question';
      statusCode = 408;
    } else if (error.message.includes('LLM API error')) {
      errorMessage = error.message;
      statusCode = 502;
    } else if (error.message.includes('Empty or invalid response')) {
      errorMessage = 'The AI model returned an empty response. Please try rephrasing your question.';
      statusCode = 502;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
});

// API versioned chat endpoint
app.post('/api/chat', upload.single('file'), async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string' || query.trim() === '') {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    let extractedText = '';
    let fileInfo = null;

    if (req.file) {
      const filePath = req.file.path;
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      try {
        if (fileExtension === '.jpg' || fileExtension === '.jpeg' || fileExtension === '.png') {
          // For images, use multimodal processing
          fileInfo = {
            type: 'image',
            data: imageToBase64(filePath),
            mimetype: getImageMimeType(fileExtension)
          };
        } else if (fileExtension === '.pdf') {
          extractedText = await extractTextFromPDF(filePath);
        } else if (fileExtension === '.docx') {
          extractedText = await extractTextFromDOCX(filePath);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);
      } catch (error) {
        // Clean up file on error
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        throw error;
      }
    }

    if (req.file && !fileInfo && !extractedText) {
      return res.status(400).json({
        error: 'No text could be extracted from the provided file'
      });
    }

    const llmResponse = await sendToLLM(query.trim(), extractedText, fileInfo);

    // Validate response
    if (!llmResponse || typeof llmResponse !== 'string' || llmResponse.trim() === '') {
      throw new Error('Empty or invalid response from AI model');
    }

    res.json({
      success: true,
      query: query.trim(),
      extractedText: extractedText || null,
      response: llmResponse.trim()
    });

  } catch (error) {
    console.error('Error in /api/chat endpoint:', error);

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // Provide more specific error messages
    let errorMessage = 'An internal server error occurred';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout: Please try again with a simpler question';
      statusCode = 408;
    } else if (error.message.includes('LLM API error')) {
      errorMessage = error.message;
      statusCode = 502;
    } else if (error.message.includes('Empty or invalid response')) {
      errorMessage = 'The AI model returned an empty response. Please try rephrasing your question.';
      statusCode = 502;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB' });
    }
    return res.status(400).json({ error: error.message });
  }
  
  if (error.message.includes('Only .jpg, .png, .pdf, and .docx files are allowed')) {
    return res.status(400).json({ error: error.message });
  }

  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(port, () => {
  console.log(`JUBuddyAI Backend running on port ${port}`);
  console.log(`Health check: http://localhost:${port}/health`);
});