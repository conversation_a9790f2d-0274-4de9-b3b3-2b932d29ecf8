# Simple test for file upload
$uri = "http://localhost:3001/api/chat"
$filePath = "simple_test.txt"
$query = "Please solve the math problem in this file"

# Create multipart form data
$boundary = [System.Guid]::NewGuid().ToString()
$LF = "`r`n"

# Read file content
$fileContent = Get-Content $filePath -Raw -Encoding UTF8
$fileBytes = [System.Text.Encoding]::UTF8.GetBytes($fileContent)

# Build multipart body with proper formatting
$bodyLines = @()
$bodyLines += "--$boundary"
$bodyLines += 'Content-Disposition: form-data; name="query"'
$bodyLines += ''
$bodyLines += $query
$bodyLines += "--$boundary"
$bodyLines += 'Content-Disposition: form-data; name="file"; filename="simple_test.txt"'
$bodyLines += 'Content-Type: text/plain'
$bodyLines += ''

# Create the body with proper line endings
$bodyText = ($bodyLines -join $LF) + $LF
$bodyBytes = [System.Text.Encoding]::UTF8.GetBytes($bodyText)

# Add file content
$bodyBytes += $fileBytes

# Add final boundary with proper line endings
$finalBoundary = $LF + "--$boundary--" + $LF
$bodyBytes += [System.Text.Encoding]::UTF8.GetBytes($finalBoundary)

try {
    Write-Host "Testing file upload..."
    $response = Invoke-RestMethod -Uri $uri -Method POST -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyBytes
    Write-Host "SUCCESS! File processing worked!"
    Write-Host "Query: $($response.query)"
    Write-Host "Extracted Text: $($response.extractedText)"
    Write-Host "Response: $($response.response)"
} catch {
    Write-Host "ERROR: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}
