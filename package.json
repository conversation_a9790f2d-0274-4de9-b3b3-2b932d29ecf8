{"name": "jubuddy-ai-backend", "version": "1.0.0", "description": "Backend for JUBuddyAI1 Android app - Node.js Express chatbot with OCR and LLM integration", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": ["chatbot", "ocr", "llm", "express", "nodejs"], "author": "", "license": "ISC", "dependencies": {"express": "^4.19.2", "multer": "^1.4.5-lts.1", "axios": "^1.7.2", "pdf-parse": "^1.1.1", "mammoth": "^1.7.2", "dotenv": "^16.4.5", "tesseract.js": "^5.1.0", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.1.4"}, "engines": {"node": ">=16.0.0"}}